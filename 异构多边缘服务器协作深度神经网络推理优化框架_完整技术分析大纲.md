# 剩余电量敏感的异构多边缘服务器协作深度神经网络推理优化框架 - 完整技术分析大纲

## 1. 项目概述与核心创新

### 1.1 核心问题定义与研究目标
- **研究目标**: 在**满足推理精度要求**的前提下，对异构边缘环境下DNN协同推理任务进行**剩余电量敏感的能耗与吞吐量**联合优化，实现电池驱动设备的长期可持续运行。

- **核心创新点**:
  1. **剩余电量敏感优化**: 首次将设备剩余电量作为优化的核心驱动因素，通过自适应权重机制 α_m(B_m(t)) 动态平衡能耗与性能
  2. **精度约束下的联合优化**: 建立精度损失函数 Acc_loss(p_k, e_k)，在保证推理精度底线的前提下优化系统性能
  3. **异构协作感知**: 设计面向异构边缘服务器的协作调度机制，充分利用不同服务器的计算特长
  4. **多维决策耦合解耦**: 通过Battery-Aware MADDPG (BA-MADDPG)算法协同优化模型分割点、卸载策略、频率调节等高度耦合变量

- **技术挑战与解决方案**:
  - **挑战1 - 电量衰减的非线性影响**: 电池容量随时间和使用模式非线性衰减，影响长期优化策略
    - *解决方案*: 引入Peukert效应和温度补偿的精确电池模型，实现电量状态的准确预测
  - **挑战2 - 精度-性能-能耗三维权衡**: 推理精度、系统性能、能耗之间存在复杂的非线性耦合关系
    - *解决方案*: 建立基于深度学习的精度损失预测模型，实现三维权衡的精确量化
  - **挑战3 - 异构环境下的协作复杂性**: 不同类型边缘服务器的计算能力、专业化程度差异巨大
    - *解决方案*: 设计服务器能力画像机制，实现任务与服务器的智能匹配
  - **挑战4 - 动态环境的实时适应**: 网络条件、服务器负载、设备电量状态实时变化
    - *解决方案*: 基于多智能体强化学习的在线自适应优化算法

### 1.2 系统架构层次 (电量感知的分层设计)
```
┌─────────────────────────────────────────────────────────────┐
│                    实验评估层                                │
│         (experimental_evaluation.py)                        │
│    [电量敏感性实验 | 精度权衡实验 | 长期运行实验]              │
├─────────────────────────────────────────────────────────────┤
│                    理论分析层                                │
│            (convergence_analysis.py)                        │
│      [BA-MADDPG收敛性 | 电量约束稳定性 | 复杂度分析]          │
├─────────────────────────────────────────────────────────────┤
│                   算法设计层                                 │
│             (ba_maddpg_algorithm.py)                        │
│    [电量感知状态空间 | 自适应奖励函数 | 约束处理机制]          │
├─────────────────────────────────────────────────────────────┤
│                   优化问题层                                 │
│            (optimization_problem.py)                        │
│      [多目标函数 | 精度约束 | 电量约束 | 协作约束]            │
├─────────────────────────────────────────────────────────────┤
│                   性能模型层                                 │
│            (performance_models.py)                          │
│    [精确能耗模型 | 吞吐量模型 | 电量敏感权重机制]              │
├─────────────────────────────────────────────────────────────┤
│                 精度约束建模层                               │
│            (accuracy_constraint.py)                         │
│      [精度损失函数 | 早退出策略 | 精度预测模型]              │
├─────────────────────────────────────────────────────────────┤
│                   系统建模层                                 │
│              (system_model.py)                              │
│  [精确电池模型 | 异构设备建模 | 协作服务器建模 | 网络建模]     │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 技术栈与依赖 (电量感知增强)
- **深度学习**: PyTorch 1.12+, TorchVision, ONNX (模型优化)
- **强化学习**: Gym, Stable-Baselines3, Ray[RLlib] (分布式训练)
- **电池建模**: SciPy (数值求解), Pandas (数据处理)
- **数值计算**: NumPy, SciPy, Numba (加速计算)
- **可视化**: Matplotlib, Seaborn, Plotly, Tensorboard
- **并行计算**: Ray, Multiprocessing, CUDA (GPU加速)
- **系统监控**: psutil (系统资源监控), GPUtil (GPU监控)

## 2. 系统建模层 (system_model.py)

### 2.1 精确电池建模 (核心创新)
```python
class BatteryModel:
    """精确电池模型 - 考虑Peukert效应、温度影响、循环衰减"""

    def __init__(self, nominal_capacity, peukert_exponent, temperature_coeff):
        self.C_nominal = nominal_capacity      # 标称容量 (mAh)
        self.n = peukert_exponent             # Peukert指数 (1.1-1.3)
        self.k_temp = temperature_coeff       # 温度系数 (0.006/°C)
        self.cycle_count = 0                  # 循环次数
        self.temperature = 25.0               # 当前温度 (°C)

    def effective_capacity(self, discharge_rate, temperature, cycle_count):
        """计算有效容量 - Peukert效应 + 温度补偿 + 循环衰减"""
        # Peukert效应: C_eff = C_nominal * (I_nominal/I_actual)^(n-1)
        peukert_factor = (1.0 / discharge_rate) ** (self.n - 1)

        # 温度补偿: 每降低1°C容量减少0.6%
        temp_factor = 1 + self.k_temp * (temperature - 25.0)

        # 循环衰减: 每1000次循环容量衰减20%
        cycle_factor = max(0.8, 1 - 0.0002 * cycle_count)

        return self.C_nominal * peukert_factor * temp_factor * cycle_factor

class LocalDevice:
    """异构边缘设备建模 - 电量感知增强"""

    def __init__(self, device_id, device_type, battery_config):
        self.device_id = device_id
        self.device_type = device_type  # "jetson_xavier", "jetson_nano", "raspberry_pi"

        # 精确电池建模
        self.battery = BatteryModel(**battery_config)
        self.current_battery = self.battery.C_nominal  # 当前剩余电量 (mAh)
        self.battery_voltage = 5.0  # 电池电压 (V)

        # 设备状态向量增强: s_m = [B_ratio, B_health, T_device, f_current, load_history]
        self.state_dim = 8  # 扩展状态维度

        # 计算能力配置 (异构特性)
        self.compute_specs = self._init_compute_specs()

        # 能耗特性参数
        self.power_model = self._init_power_model()

    def _init_compute_specs(self):
        """初始化异构计算规格"""
        specs = {
            "jetson_xavier": {
                "gpu_freq_range": [0.12e9, 1.10e9],  # Hz
                "cpu_cores": 8,
                "gpu_cores": 512,
                "memory": 32,  # GB
                "peak_flops": 21e12  # FLOPS
            },
            "jetson_nano": {
                "gpu_freq_range": [0.08e9, 0.92e9],
                "cpu_cores": 4,
                "gpu_cores": 128,
                "memory": 4,
                "peak_flops": 0.5e12
            }
        }
        return specs.get(self.device_type, specs["jetson_xavier"])

    def _init_power_model(self):
        """初始化功耗模型参数"""
        return {
            "kappa": 1.3e-9,      # 动态功耗系数 (W/Hz^3)
            "P_static": 15.0,     # 静态功耗 (W)
            "P_idle": 5.0,        # 空闲功耗 (W)
            "efficiency": 0.85    # 电源效率
        }
```

**电池建模核心参数**:
- **C_nominal**: 电池标称容量 (3000-5000 mAh)
- **n (Peukert指数)**: 放电倍率对容量的影响系数 (1.1-1.3)
- **k_temp**: 温度系数 (0.006/°C，每降低1°C容量减少0.6%)
- **cycle_count**: 电池循环次数 (影响容量衰减)
- **discharge_rate**: 当前放电倍率 (C-rate)
- **temperature**: 设备工作温度 (°C)

**异构设备特性参数**:
- **device_type**: 设备类型 ("jetson_xavier", "jetson_nano", "raspberry_pi")
- **gpu_freq_range**: GPU频率调节范围 (Hz)
- **peak_flops**: 峰值计算能力 (FLOPS)
- **memory**: 设备内存容量 (GB)
- **power_model**: 设备特定的功耗模型参数

### 2.2 异构边缘服务器建模 (协作感知)
```python
class EdgeServer:
    """异构边缘服务器建模 - 支持协作调度"""

    def __init__(self, server_id, server_type, specialization_profile):
        self.server_id = server_id
        self.server_type = server_type  # "gpu_server", "cpu_cluster", "fpga_accelerator"

        # 服务器状态向量: s_n = [f_server, load_ratio, specialization_match, energy_efficiency]
        self.state_dim = 6

        # 计算能力配置
        self.compute_specs = self._init_server_specs()

        # 专业化能力画像 (核心创新)
        self.specialization = specialization_profile  # 各层类型的处理效率

        # 协作调度参数
        self.collaboration_params = self._init_collaboration_params()

        # 当前状态
        self.current_load = 0.0      # 当前负载率 [0,1]
        self.active_tasks = []       # 当前处理的任务列表
        self.energy_consumption = 0.0 # 累计能耗 (J)

    def _init_server_specs(self):
        """初始化服务器计算规格"""
        specs = {
            "gpu_server": {
                "freq_range": [2.2e9, 2.8e9],  # Hz
                "max_load": 0.8,
                "cores": 4096,  # CUDA cores
                "memory": 80,   # GB
                "peak_flops": 125e12,  # FLOPS
                "power_consumption": 300  # W
            },
            "cpu_cluster": {
                "freq_range": [1.8e9, 3.2e9],
                "max_load": 0.9,
                "cores": 64,    # CPU cores
                "memory": 256,
                "peak_flops": 2e12,
                "power_consumption": 200
            },
            "fpga_accelerator": {
                "freq_range": [0.1e9, 0.5e9],
                "max_load": 0.95,
                "cores": 2048,  # Logic elements
                "memory": 32,
                "peak_flops": 10e12,
                "power_consumption": 150
            }
        }
        return specs.get(self.server_type, specs["gpu_server"])

    def _init_collaboration_params(self):
        """初始化协作调度参数"""
        return {
            "task_queue_capacity": 100,
            "load_balancing_threshold": 0.7,
            "migration_cost": 0.1,  # 任务迁移开销
            "communication_bandwidth": 1e9,  # bps
            "coordination_overhead": 0.05  # 协调开销比例
        }

    def calculate_specialization_efficiency(self, layer_type):
        """计算对特定层类型的处理效率"""
        base_efficiency = self.specialization.get(layer_type, 1.0)
        load_penalty = max(0.5, 1 - self.current_load * 0.5)
        return base_efficiency * load_penalty

    def estimate_processing_time(self, computation_load, layer_type):
        """估算处理时间 - 考虑专业化和负载"""
        efficiency = self.calculate_specialization_efficiency(layer_type)
        effective_freq = self.compute_specs["freq_range"][1] * efficiency
        return computation_load / effective_freq
```

**异构服务器核心参数**:
- **server_type**: 服务器类型 ("gpu_server", "cpu_cluster", "fpga_accelerator")
- **specialization_profile**: 专业化能力画像，定义对不同层类型的处理效率
  - conv: 卷积层处理效率 (GPU服务器: 1.5, CPU集群: 0.8, FPGA: 1.2)
  - fc: 全连接层处理效率 (GPU服务器: 1.3, CPU集群: 1.1, FPGA: 0.9)
  - bn: 批归一化处理效率 (GPU服务器: 1.0, CPU集群: 1.2, FPGA: 1.1)
- **freq_range**: 工作频率范围 (Hz)
- **max_load**: 最大允许负载率 (0.8-0.95)
- **peak_flops**: 峰值计算能力 (FLOPS)
- **power_consumption**: 功耗 (W)

**协作调度参数**:
- **task_queue_capacity**: 任务队列容量
- **load_balancing_threshold**: 负载均衡触发阈值
- **migration_cost**: 任务迁移开销系数
- **communication_bandwidth**: 服务器间通信带宽 (bps)
- **coordination_overhead**: 协调开销比例

### 2.3 DNN推理任务模型 (集成精度与早退出)
```python
class EdgeInferenceTask:
    # 模型: ResNet-50 with Early Exits (在cifar-10上预训练)
    # 决策变量: a_k = (p_m, f_m, n_m)
    # p_k: 分割点 (包含本地或服务器完整执行的特殊情况)
    # e_k: 早退出点,在MADDPG中是提前知晓的，选择满足精确度要求的最小早退出点 (指定在哪个中间层退出推理)
    # f_k: 本地CPU频率
    #n_m: 目标卸载服务器索引
```

**核心参数详解**:
- **Dataset**: **cifar-10** (作为基准数据集进行精度评估)
- **Base Model**: ResNet-50 with Early Exits (添加多个辅助分类器)
- **分割与卸载决策 (p_k)**: 
  - p_k = 0: 完全本地执行
  - p_k = L+1: 完全卸载至服务器n
  - 1 <= p_k <= L: 在第p_k层后分割，前半部分本地执行，后半部分卸载
  - **融合**: 该定义自然地将“卸载位置”和“分割点”融为一体。选择分割点p_k，也就隐式地决定了卸载位置（即处理后半部分的服务器）。
- **早退出策略 (e_k)**: 
  - e_k ∈ {Full, Exit_1, Exit_2, Exit_3, Exit_4, Exit_5}: 指定在模型的哪个深度退出推理，提供不同级别的精度-计算量权衡（早退出可产生显著的精度差异，同时减少计算负载）。
  - **数据驱动的精度损失模型**: Acc_loss(e_k) - 通过离线剖析或在线估计，建立早退出点与cifar-10验证集上Top-1精度损失的映射关系。
- **计算复杂度 (FLOPs)**: C(p_k, e_k)=C_local(p_k, e_k) + C_edge(p_k, e_k)，其中 C_local 是本地计算量，C_edge 是边缘计算量。 - 计算量现在是分割点和早退出点的函数。
- **数据传输量 (DataSize)**: D(p_k) - 传输的数据量仅与分割点相关。
- **决策流程**:
  1. 根据Acc_min从{Full, Exit_1, Exit_2, Exit_3, Exit_4, Exit_5}中选择满足条件的最小早退出点
  2. 确定早退出点后，将模型截断至该点
  3. 在截断后的模型上优化分割点p_k(≤e_k)、服务器选择和CPU频率

**层级信息参数**:
- **layers_info**: 每层的详细信息列表
  - **layer_id**: 层的唯一标识 (0, 1, 2, ..., 49)
  - **layer_type**: 层类型 ("conv", "bn", "activation", "pool", "fc")
  - **input_shape**: 输入张量形状 (C, H, W)
  - **output_shape**: 输出张量形状 (C', H', W')
  - **flops**: 该层的计算复杂度 (FLOPs)
  - **params**: 该层的参数数量
  - **output_size**: 输出数据大小 (字节)
  - **has_exit**: 是否有早退出分支 (boolean)

### 2.4 网络环境建模
```python
# 信道增益矩阵: G_m,n(t) = G_0 * d_m,n^(-α) * h_m,n(t)
# 带宽分配矩阵: B_m,n(t) ∈ [1, 100] MHz
# 传输速率: R_m,n(t) = B_m,n(t) * log2(1 + SNR_m,n(t))
```

**网络参数详解**:
- **G_m,n(t)**: 设备m到服务器n在时刻t的信道增益 (无量纲)
- **G_0**: 参考距离处的信道增益 (通常取1.0)
- **d_m,n**: 设备m到服务器n的物理距离 (米)
- **α**: 路径损耗指数 (自由空间α=2, 城市环境α=3-4)
- **h_m,n(t)**: 时变小尺度衰落系数 (瑞利分布, 均值1.0)
- **B_m,n(t)**: 设备m到服务器n分配的带宽 (Hz)
- **R_m,n(t)**: 设备m到服务器n的传输速率 (bps)
- **SNR_m,n(t)**: 信噪比 = P_tx * G_m,n(t) / N_0 (无量纲)

**物理层参数**:
- **P_tx**: 传输功率 (0.1W ~ 2.0W)
- **N_0**: 噪声功率谱密度 (-174 dBm/Hz)
- **distance_matrix**: M×N距离矩阵 (10m ~ 200m)
- **bandwidth_matrix**: M×N带宽分配矩阵 (1MHz ~ 100MHz)
- **channel_gain_matrix**: M×N信道增益矩阵 (固定)


## 3. 优化问题层 (optimization_problem.py)

### 3.1 决策变量
- **联合决策向量**: 对于每个设备m，决策向量为 `a_m = (p_m, f_m, n_m)`
  - `p_m`: DNN分割点 (0 到 L+1, ≤ e_m)
  - `e_m`: 早退出点，在MADDPG中是提前知晓的，选择满足精确度要求的最小早退出点 (指定退出层，首先基于Acc_min选择)
  - `f_m`: 本地GPU频率
  - `n_m`: 目标卸载服务器索引

### 3.2 目标函数：剩余电量敏感的能耗与吞吐量加权和
**最大化** `J(t) = Σ_m [α_m * R_m(t) - (1-α_m) * E_total,m(t)]`

- **能耗项 `E_total,m(t)`**: 包含本地计算、卸载通信的综合能耗（只考虑本地）。
- **吞吐项 `R_m(t)`**: 1/完成一次推理任务的总时间=1/T_m(t).
- **总延迟项  T_m(t)=完成一次推理任务的总时间。
- **电量敏感权重 `α_m(B_m(t))`**: `α_m` 是一个关于剩余电量 `B_m(t)` 的单调递增函数，例如 `α_m =1/(1+exp(-β * (B_m(t) / B_max_m - θ)))`。当电量低时，权重减小，系统更倾向于节能。
**θ**: 电量阈值，当电量低于此值时，系统更倾向于节能(固定0.4)。β=10.

### 3.3 约束条件
1.  **精度约束 (核心创新点)**:
    - `Acc(p_m, e_m) >= Acc_min`范围是[0.7,0.91]
    - `Acc(p_m, e_m)` 是通过离线剖析得到的，关于分割点和早退出点的精度函数。
    - `Acc_min` 是用户定义的最低可接受精度
    - **创新性分析**: 此约束将优化问题从“尽可能好”转变为“在满足底线要求下的最优化”，更符合实际应用。通过动态调整`Acc_min`，可以探索不同应用场景下的性能边界，例如，在电量极其宝贵时，适当降低精度要求以换取生存时间。

2.  **电池容量约束**: `B_m(t) > B_min`
3.  **GPU频率约束**: `f_m ∈ [f_min, f_max]`
4.  **服务器容量约束**: `Σ_m I(n_m=n) * C_edge(p_m, e_m) <= 边缘服务器频率f_server*max_load_n*Δt` (其中I是指示函数)      《Energy-Efﬁcient Service Placement for Latency-Sensitive Applications in Edge Computing》
5.  **延迟约束**: `T_m(t) <= T_max`

## 7. 实验评估层 (experimental_evaluation.py)

### 7.1 实验设置
- **数据集**: cifar-10
- **硬件环境**: 模拟NVIDIA Jetson系列设备和不同类型的边缘服务器。
- **基线算法 (Baselines)**:
  1.  **Local-Only (本地执行)**: 所有任务在本地完整执行，无早退出。
  2.  **Edge-Only (完全卸载)**: 所有任务卸载到最优服务器，完整执行。
  3.  **Greedy-Accuracy (精度优先)**: 总是选择完整执行策略，不早退出，不考虑能耗和延迟。
  4.  **Greedy-Energy (能耗优先)**: 在满足最低精度`Acc_min`的前提下，选择能耗最低的早退出和分割组合。
  5.  **Random**: 随机选择所有决策变量。

### 7.2 评估指标
- **主要指标**: 
  - **系统总能耗**: 所有设备能耗之和。
  - **平均任务完成时间（吞吐量）**。
  - **平均剩余电量**。
- **辅助指标**: 平均选择的早退出点、分割点分布、服务器负载均衡情况。

### 7.3 核心实验分析
1.  **性能对比**: 将我们提出的MADDPG算法与所有基线在主要指标上进行对比。
2.  **精度-性能权衡分析 **:
    - **实验设计**: 运行多组实验，每组设置不同的最低精度要求 `Acc_min`范围是[0.7,0.91] 。
    - **结果展示**: 绘制“平均能耗随episode变化图”，“平均吞吐量随episode变化图”以及“平均剩余电量随episode变化图”。
3.  **收敛性与稳定性分析**: 展示MADDPG在训练过程中的奖励曲线变化，证明算法的有效性和稳定性。

## 8. 理论分析层 (convergence_analysis.py)

### 5.1 MADDPG智能体设计
- **状态空间 (State)**: 每个智能体（设备）的观测值应包含：
  - **本地状态**: 剩余电量 `B_m(t)`，G前CPU频率 `f_m(t)`。
  - **环境状态**: 各个服务器的容量约束与其他设备间的信道增益 `G_m,j(t)`。
  - **任务信息**: 当前任务的输入数据大小。

- **动作空间 (Action)**: 离散/连续混合动作空间。
  - **分割点 `p_m`**: 离散动作 (L+2个选项)。
  - **早退出点 `e_m`**: 在MADDPG中考虑，但不由actor网络给出动作，由`Acc_min`决定。
  - **CPU频率 `f_m`**: 连续或离散化动作。
  - **卸载服务器 `n_m`**: 离散动作 (N个服务器选项)。
  - **处理**: 使用Gumbel-Softmax等技术处理离散动作，或将所有动作离散化。

- **奖励函数 (Reward)**: 这是体现创新的关键。
  - `r_m(t) = J_m(t) - P(t)`
  - `J_m(t)`: 单个设备的目标函数值 `α_m R_m -(1-α_m) E_m`。
  - `P(t)`: **惩罚项**，用于处理约束。
    - **精度惩罚**: 如果 `Acc(p_m, e_m) < Acc_min`，则施加一个巨大的负惩罚 `P_acc`。
    - **其他约束惩罚**: 对违反服务器容量、延迟等约束的行为施加相应惩罚。
  - **创新性**: 这个奖励函数直接引导智能体在满足精度“硬约束”的前提下，去优化能耗和延迟的“软目标”。

### 5.2 训练与收敛
- **课程学习 (Curriculum Learning)**: 可以考虑从一个较高的`Acc_min`（例如无损精度）开始训练，然后逐步放宽约束，让智能体学习在不同精度要求下的最优策略，加速收敛。
- **收敛性分析**: 监控平均奖励、精度满足率、以及在ImageNet100验证集上的实际端到端性能指标。

## 6. 性能模型层 (performance_models.py)

### 3.1 能耗模型 (EnergyModel)
**数学基础**:
```
E_total,m(t) = E_comp,m(t) + E_comm,m(t) + E_idle,m(t)

E_comp = κ  * f^3 * Δt          # CMOS功耗理论     《DVFS-Aware DNN Inference on GPUs: Latency Modeling and Performance Analysis》

E_comm = P_tx * D_tx / R          # 通信功耗
E_idle = P_static *  Δt       # 静态功耗
Δt表示当前设备单次本地推理时间=本地任务计算量Flops/f_m(t)。
```

**能耗参数详解**:
- **E_total,m(t)**: 设备m在时刻t的总能耗 (焦耳)
- **E_comp,m(t)**: 计算能耗 (焦耳)
- **E_comm,m(t)**: 通信能耗 (焦耳)
- **E_idle,m(t)**: 静态能耗 (焦耳)
- **κ (kappa)**: 设备特定的能耗系数 (1.3W/Hz^3)    《Power efficient time-sensitive mapping in heterogeneous systems》
- **f**: GPU工作频率 (Hz)
- **Δt**: 时间间隔,表示当前设备单次本地推理时间=本地任务计算量Flops/f_m(t)。

**通信能耗参数**:
- **P_tx**: 传输功率 (瓦特, 0.1W ~ 2.0W)
- **D_tx**: 传输数据量 (字节)
- **R**: 传输速率 (字节/秒)
- **T_tx**: 传输时间 = D_tx / R (秒)

**静态能耗参数**:
- **P_static**: 静态功耗 (瓦特)
  - Jetson Xavier: [10,30]W
- **T_idle**: 总时间，只要启动就会一直产生静态功耗 (秒)


### 3.2 吞吐量模型 (ThroughputModel)
**数学基础**:
```
R_sys(t) = 1 / T_m(t)     # 系统吞吐量

T_m(t) = T_local  + T_comm + T_edge

T_local = C_local / f_m          # 本地处理时间
T_comm = D_tx / R_tx            # 通信延迟
T_edge = C_edge / f_server      # 边缘处理时间
```

**吞吐量参数详解**:
- **R_sys(t)**: 系统在时刻t的总吞吐量 (任务/秒)
- **T_m(t)**: 设备m的端到端延迟 (秒)

**延迟组件参数**:
- **T_local**: 本地处理延迟 (秒)
  - **C_local**: 本地计算复杂度 (FLOPs)
  - **f_m**: 设备m的CPU频率 (Hz)
- **T_comm**: 通信传输延迟 (秒)
  - **D_tx**: 传输数据量 (字节)
  - **R_tx**: 传输速率 (字节/秒)
- **T_edge**: 边缘处理延迟 (秒)
  - **C_edge**: 边缘计算复杂度 (FLOPs)
  - **f_server**: 服务器等效频率 (Hz)

**系统性能参数**:
- **device_delays**: 所有设备延迟列表 [T_1, T_2, ..., T_M]

### 3.3 自适应权重机制 (AdaptiveWeightMechanism)
**核心算法**:
```python
α_m(t) = 1 / (1 + exp(-β(B_m(t)/B_max_m - θ)))

# 参数设置:
# β = 10.0  (敏感度参数)
# θ = 0.4  (阈值参数，当B/B_max_m = 0.4时，α_m = 0.5)
```

**权重参数详解**:
- **α_m(t)**: 设备m在时刻t的自适应权重 (0-1)
- **B_m(t)**: 设备m当前剩余电池电量 (mAh)
- **B_max_m**: 设备m的最大电池容量 (mAh)
- **B_m/B_max_m**: 电池电量比例 (0-1)
- **β (beta)**: 敏感度参数，控制权重变化的陡峭程度
  - β越大，权重变化越陡峭
  - β越小，权重变化越平缓
  - 默认值: β = 10.0
- **θ (theta)**: 阈值参数，决定权重转换的中心点
  - 当B_m(t)/B_m^max = θ时，α_m(t) = 0.5
  - θ < 0.5表示倾向于节能
  - θ > 0.5表示倾向于性能
  - 默认值: θ = 0.4

**权重语义与策略**:
- **α_m(t) → 1**: 电量充足 (B/B_max > θ)，优先吞吐量最大化
- **α_m(t) → 0**: 电量不足 (B/B_max < θ)，优先能耗最小化
- **α_m(t) ≈ 0.5**: 电量临界 (B/B_max ≈ θ)，平衡优化
- **平滑过渡**: Sigmoid函数确保权重连续变化，避免策略突变

**多设备权重计算**:
- **weights**: 所有设备权重向量 [α_1, α_2, ..., α_M]
- **weight_history**: 权重变化历史记录
- **average_weight**: 系统平均权重 = mean(weights)

## 4. 算法实现层 (maddpg_algorithm.py)

### 4.1 Actor网络架构 (去中心化执行)
```python
class ActorNetwork:
    # 混合动作空间支持
    # 离散动作: 服务器选择 ∈ {0,1,2,3}, 分割点 ∈ {1,2,...,50}
    # 连续动作: GPU频率 ∈ [f_min, f_max]

    # 网络结构: [state_dim] → [256] → [256] → [action_dim]
    # 激活函数: ReLU (隐藏层), Tanh (输出层)

    def forward(self, local_state):  # 只使用本地状态
        # 输入: 设备自身状态 [battery_ratio, channel_gains, server_loads]
        # 输出: 设备自身动作 [server_choice, split_point, frequency]
```

**去中心化执行特点**:
- **本地状态输入**: 每个设备只观察自身状态 `state_dim = 9`
  - 电池电量比例: 1维
  - 信道增益向量: 4维 (到各服务器的信道质量)
  - 服务器负载向量: 4维 (各服务器当前负载)
- **独立决策**: 设备无需与其他设备通信即可做出决策
- **实时执行**: 支持边缘环境下的低延迟决策需求
- **隐私保护**: 设备状态信息不需要共享给其他设备

### 4.2 Critic网络架构 (中心化训练)
```python
class CriticNetwork:
    # 中心化训练: 输入所有智能体状态和动作
    # 输入维度: num_agents * (state_dim + action_dim) = 10 * (9 + 3) = 120
    # 输出: 单个Q值

    # 网络结构: [total_input=120] → [256] → [256] → [1]

    def forward(self, all_states, all_actions):  # 使用全局信息
        # 输入: 所有设备状态 + 所有设备动作
        # 输出: 全局Q值评估
```

**中心化训练特点**:
- **全局状态输入**: 包含所有M个设备的状态信息
  - 总状态维度: `M × state_dim = 10 × 9 = 90`
  - 总动作维度: `M × action_dim = 10 × 3 = 30`
  - 总输入维度: `90 + 30 = 120`
- **全局价值评估**: 提供系统级的Q值估计
- **非平稳性解决**: 通过全局视角缓解多智能体环境的非平稳问题
- **协调学习**: 帮助智能体学习协作策略

### 4.3 个体化奖励设计机制
**改进方案**: 个体化奖励函数
```python
def compute_individual_rewards(self, actions, info):
    """为每个设备计算个体奖励"""
    individual_rewards = []

    for m in range(self.M):
        device = self.devices[m]

        # 设备m的自适应权重
        alpha_m = calculate_weight(device.current_battery, device.config.max_battery)

        # 设备m的个体能耗
        energy_m = info['device_energies'][m]

        # 设备m对系统吞吐量的贡献
        throughput_contribution_m = 1.0 / info['delays'][m]

        # 设备m的个体奖励
        reward_m = alpha_m * throughput_contribution_m - (1 - alpha_m) * energy_m

        # 约束惩罚
        if device.is_depleted():
            reward_m -= 100  # 电池耗尽惩罚
        if info['delays'][m] > device.max_tolerable_delay:
            reward_m -= 50   # 延迟超限惩罚

        individual_rewards.append(reward_m)

    return np.array(individual_rewards)  # 返回向量而非标量
```

**个体化奖励参数详解**:
- **alpha_m**: 设备m的个体自适应权重 (基于自身电池状态)
- **energy_m**: 设备m的个体能耗 (焦耳)
- **throughput_contribution_m**: 设备m对系统吞吐量的贡献 (1/延迟)
- **reward_m**: 设备m的个体奖励值

- **constraint_penalties**: 个体约束违反惩罚


### 4.4 MADDPG训练算法
**核心更新公式**:
```python
# Critic更新 (TD学习)
L_critic = E[(Q(s,a) - (r + γQ'(s',a')))²]
其中 a' = [π'₁(s'₁), π'₂(s'₂), ..., π'ₘ(s'ₘ)]

# Actor更新 (策略梯度)
∇J = E[∇_{a_i} Q(s,a₁,...,aₘ) ∇_θ π_i(a_i|s_i)|_{a_i=π_i(s_i)}]

# 软更新目标网络
θ' = τθ + (1-τ)θ'  (τ = 0.005)
```

**训练参数详解**:
- **L_critic**: Critic网络的损失函数 (均方误差)
- **Q(s,a)**: 当前Critic网络的Q值估计 (使用全局状态和动作)
- **r**: 即时奖励向量 [r₁, r₂, ..., rₘ] (每个设备的个体奖励)
- **γ (gamma)**: 折扣因子，控制未来奖励的重要性 (0.99)
- **Q'(s',a')**: 目标网络的Q值估计
- **a'**: 所有智能体的目标动作 [π'₁(s'₁), π'₂(s'₂), ..., π'ₘ(s'ₘ)]
- **∇J**: Actor网络的策略梯度
- **θ**: 网络参数
- **π_i(a_i|s_i)**: 智能体i的策略函数 (只依赖本地状态)
- **τ (tau)**: 软更新参数，控制目标网络更新速度 (0.005)

**中心化训练-去中心化执行流程**:
1. **训练阶段** (中心化):
   - Critic使用全局信息: `Q(s₁,...,sₘ, a₁,...,aₘ)`
   - 每个Actor接收个体奖励: `r_i`
   - 策略梯度使用全局Q值指导
2. **执行阶段** (去中心化):
   - 每个Actor只使用本地状态: `π_i(a_i|s_i)`
   - 无需设备间通信
   - 支持实时决策

**超参数配置**:
- **lr_actor**: Actor网络学习率 (1e-4)
- **lr_critic**: Critic网络学习率 (5e-4, 是Actor的5倍)
- **gamma**: 折扣因子 (0.99)
- **tau**: 软更新参数 (0.005)
- **buffer_size**: 经验回放缓冲区大小 (10000)
- **batch_size**: 训练批次大小 (64)
- **update_frequency**: 网络更新频率 (每3步更新一次)
- **warmup_steps**: 预热步数，开始训练前的经验积累 (50)

**噪声调度参数**:
- **initial_noise**: 初始探索噪声标准差 (0.4)
- **final_noise**: 最终探索噪声标准差 (0.08)
- **noise_decay_episodes**: 噪声衰减的episode数 (总episode数的80%)
- **current_noise_std**: 当前噪声标准差 (动态调整)

**网络架构参数**:
- **hidden_dims**: 隐藏层维度 [128, 64]
- **dropout_rate**: Dropout比率 (0.1)
- **grad_clip**: 梯度裁剪阈值 (1.0)

## 5. 优化问题层 (optimization_problem.py)

### 5.1 多目标优化问题形式化
```
max: J(x) = Σ_m [α_m R_m-(1-α_m)E_m]

subject to:
    B_m(t) ≥ B_min=0.05B_max                    # 电池约束
    f_m ∈ [f_min, f_max]             # 频率约束
    Σ_m I(n_m=n) * C_edge(p_m, e_m) <= 边缘服务器频率f_server*max_load_n*Δt          # 服务器容量约束
    s_m ∈ {1,2,...,e_m}                # 分割点约束
    T_m(t) ≤ T_max,m=20ms                 # 延迟约束                 《Energy-Efﬁcient Service Placement for Latency-Sensitive Applications in Edge Computing》
```

**优化变量详解**:
- **J(x)**: 系统总目标函数 (最大化)
- **x**: 优化变量向量，包含所有决策变量
- **α_m**: 设备m的自适应权重 (0-1)
- **E_m**: 设备m的能耗 (焦耳)
- **R_m**: 设备m的吞吐量贡献 (任务/秒)
- **(1-α_m)**: 能耗项权重，α_m越小越重视能耗
- **α_m**: 吞吐量项权重，α_m越大越重视性能
- **T_m(t)**: T_m(t) = T_local,m + T_comm,m + T_edge,m 
- **T_max,m **: 任务的最大容忍延迟=20ms

**决策变量组成**:
- **server_allocation**: 服务器分配向量 [n_1, n_2, ..., n_M]
  - **n_m ∈ {0,1,2,3}**: 设备m选择的服务器ID
- **split_points**: 分割点向量 [s_1, s_2, ..., s_M]
  - **s_m ∈ {1,2,...,e_m}**: 设备m的DNN分割点
- **frequencies**: 频率向量 [f_1, f_2, ..., f_M]
  - **f_m ∈ [f_min, f_max]**: 设备m的CPU工作频率

**约束条件详解**:
- **电池约束**: B_m(t) ≥ B_min
  - **B_min**: 最小电池电量阈值 (通常为最大容量的5%)
  - 防止设备电量耗尽
- **频率约束**: f_m ∈ [f_min, f_max]
  - **f_min**: 最小工作频率 (0.12GHz)
  - **f_max**: 最大工作频率 (1.10GHz)
- **服务器容量约束**: Σ_m I(n_m=n) * C_edge(p_m, e_m) <= 边缘服务器频率f_server*max_load_n*Δt

- **分割点约束**: s_m ∈ {1,2,...,e_m}
  - **e_k**: 早退出点
  - 确保分割点在有效范围内
  - **延迟约束**:  T_m(t) ≤ T_max,m   任务的推理时间要小于最大容忍延迟